// Smooth scrolling function
function scrollToCountries() {
    document.getElementById('countries').scrollIntoView({
        behavior: 'smooth'
    });
}

// Scroll animations
function animateOnScroll() {
    const elements = document.querySelectorAll('.country-row, .highlight-item');

    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }
    });
}

// Initialize animations
function initAnimations() {
    const elements = document.querySelectorAll('.country-row, .highlight-item');

    elements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'all 0.6s ease';
    });
}

// Parallax effect for hero section
function parallaxEffect() {
    const hero = document.querySelector('.hero');
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;

    if (hero) {
        hero.style.transform = `translateY(${rate}px)`;
    }
}

// Add hover effects to table rows
function addTableEffects() {
    const rows = document.querySelectorAll('.country-row');

    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}

// Smooth scroll for navigation
function smoothScrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Add loading animation
function addLoadingAnimation() {
    const body = document.body;
    body.style.opacity = '0';

    window.addEventListener('load', function() {
        body.style.transition = 'opacity 0.5s ease';
        body.style.opacity = '1';
    });
}

// Intersection Observer for animations
function setupIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements
    const elementsToObserve = document.querySelectorAll('.country-row, .highlight-item');
    elementsToObserve.forEach(element => {
        observer.observe(element);
    });
}

// Add CSS for intersection observer animations
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .country-row, .highlight-item {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .country-row.animate-in, .highlight-item.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .country-row:nth-child(1) { transition-delay: 0.1s; }
        .country-row:nth-child(2) { transition-delay: 0.2s; }
        .country-row:nth-child(3) { transition-delay: 0.3s; }
        .country-row:nth-child(4) { transition-delay: 0.4s; }
        .country-row:nth-child(5) { transition-delay: 0.5s; }

        .highlight-item:nth-child(1) { transition-delay: 0.1s; }
        .highlight-item:nth-child(2) { transition-delay: 0.2s; }
        .highlight-item:nth-child(3) { transition-delay: 0.3s; }
        .highlight-item:nth-child(4) { transition-delay: 0.4s; }
    `;
    document.head.appendChild(style);
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addLoadingAnimation();
    addAnimationStyles();
    setupIntersectionObserver();
    addTableEffects();

    // Add scroll event listeners
    window.addEventListener('scroll', function() {
        parallaxEffect();
    });

    // Add click event to scroll indicator
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', scrollToCountries);
    }
});

// Add smooth scrolling to all internal links
document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href').substring(1);
        smoothScrollToSection(targetId);
    }
});

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowDown' && e.ctrlKey) {
        e.preventDefault();
        scrollToCountries();
    }
});

// Performance optimization: throttle scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Apply throttling to scroll events
window.addEventListener('scroll', throttle(function() {
    parallaxEffect();
}, 16)); // ~60fps
