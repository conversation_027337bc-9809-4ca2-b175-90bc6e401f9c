#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube Short مبسط - 5 حقائق مدهشة
نسخة مبسطة تتجنب مشاكل الصلاحيات
"""

import os
import tempfile
from PIL import Image, ImageDraw, ImageFont
import numpy as np
from moviepy.editor import *

class SimpleYouTubeShortCreator:
    def __init__(self):
        self.width = 1080
        self.height = 1920
        self.fps = 30
        self.slide_duration = 10
        self.intro_duration = 5
        self.outro_duration = 5
        
        # الحقائق المدهشة
        self.facts = [
            {
                'title': '🌌 حقيقة فضائية مذهلة',
                'text': 'يمكن لمليون كرة أرضية\nأن تتسع داخل الشمس!\nالشمس أكبر من الأرض\nبـ 1.3 مليون مرة 🤯',
                'emoji': '🌞',
                'background_color': ['#667eea', '#764ba2']
            },
            {
                'title': '🧠 حقيقة عن الدماغ',
                'text': 'دماغك يستهلك 20%\nمن طاقة جسمك رغم أنه\nيشكل 2% فقط من وزنك!\nإنه كمبيوتر خارق 💻',
                'emoji': '🧠',
                'background_color': ['#f093fb', '#f5576c']
            },
            {
                'title': '🌍 حقيقة جغرافية',
                'text': 'المحيط الهادئ أكبر\nمن كل اليابسة مجتمعة!\nيغطي ثلث سطح الأرض\nويحتوي على 50% من المياه 🌊',
                'emoji': '🌊',
                'background_color': ['#4facfe', '#00f2fe']
            },
            {
                'title': '🏛️ حقيقة تاريخية',
                'text': 'الأهرامات بُنيت قبل\nوجود الماموث!\nآخر ماموث عاش\nقبل 4000 سنة فقط 🐘',
                'emoji': '🏺',
                'background_color': ['#fa709a', '#fee140']
            },
            {
                'title': '🦋 حقيقة طبيعية',
                'text': 'الفراشات تتذوق بأقدامها!\nولديها مستقبلات تذوق\nفي أرجلها للعثور\nعلى الطعام المناسب 👅',
                'emoji': '🦋',
                'background_color': ['#a8edea', '#fed6e3']
            }
        ]

    def create_gradient_background(self, colors, width, height):
        """إنشاء خلفية متدرجة"""
        image = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(image)
        
        for y in range(height):
            ratio = y / height
            color1 = tuple(int(colors[0][i:i+2], 16) for i in (1, 3, 5))
            color2 = tuple(int(colors[1][i:i+2], 16) for i in (1, 3, 5))
            
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        return image

    def get_font(self, size):
        """الحصول على خط مناسب"""
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            None
        ]
        
        for font_path in font_paths:
            try:
                if font_path and os.path.exists(font_path):
                    return ImageFont.truetype(font_path, size)
            except:
                continue
        return ImageFont.load_default()

    def create_intro_slide(self):
        """إنشاء شريحة المقدمة"""
        background = self.create_gradient_background(['#667eea', '#764ba2'], self.width, self.height)
        draw = ImageDraw.Draw(background)
        
        title_font = self.get_font(80)
        subtitle_font = self.get_font(50)
        
        # العنوان الرئيسي
        title = "🔥 5 حقائق مذهلة 🔥"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = 600
        
        # تأثير الظل
        draw.text((title_x + 3, title_y + 3), title, font=title_font, fill='#000000')
        draw.text((title_x, title_y), title, font=title_font, fill='#FFFFFF')
        
        # العنوان الفرعي
        subtitle = "ستصدمك هذه المعلومات! 🤯✨"
        subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (self.width - subtitle_width) // 2
        subtitle_y = 750
        
        draw.text((subtitle_x + 2, subtitle_y + 2), subtitle, font=subtitle_font, fill='#000000')
        draw.text((subtitle_x, subtitle_y), subtitle, font=subtitle_font, fill='#FFD23F')
        
        return background

    def create_fact_slide(self, fact, fact_number):
        """إنشاء شريحة حقيقة"""
        background = self.create_gradient_background(fact['background_color'], self.width, self.height)
        draw = ImageDraw.Draw(background)
        
        number_font = self.get_font(120)
        title_font = self.get_font(60)
        text_font = self.get_font(45)
        emoji_font = self.get_font(150)
        
        # رقم الحقيقة
        number_text = f"#{fact_number}"
        number_bbox = draw.textbbox((0, 0), number_text, font=number_font)
        number_width = number_bbox[2] - number_bbox[0]
        number_x = (self.width - number_width) // 2
        number_y = 200
        
        draw.text((number_x + 3, number_y + 3), number_text, font=number_font, fill='#000000')
        draw.text((number_x, number_y), number_text, font=number_font, fill='#FFFFFF')
        
        # الرمز التعبيري
        emoji = fact['emoji']
        emoji_bbox = draw.textbbox((0, 0), emoji, font=emoji_font)
        emoji_width = emoji_bbox[2] - emoji_bbox[0]
        emoji_x = (self.width - emoji_width) // 2
        emoji_y = 350
        
        draw.text((emoji_x, emoji_y), emoji, font=emoji_font)
        
        # العنوان
        title = fact['title']
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = 550
        
        draw.text((title_x + 2, title_y + 2), title, font=title_font, fill='#000000')
        draw.text((title_x, title_y), title, font=title_font, fill='#FFFFFF')
        
        # النص الرئيسي
        text_lines = fact['text'].split('\n')
        y_offset = 700
        
        for line in text_lines:
            line_bbox = draw.textbbox((0, 0), line, font=text_font)
            line_width = line_bbox[2] - line_bbox[0]
            line_x = (self.width - line_width) // 2
            
            draw.text((line_x + 2, y_offset + 2), line, font=text_font, fill='#000000')
            draw.text((line_x, y_offset), line, font=text_font, fill='#FFFFFF')
            y_offset += 60
        
        return background

    def create_outro_slide(self):
        """إنشاء شريحة الخاتمة"""
        background = self.create_gradient_background(['#fa709a', '#fee140'], self.width, self.height)
        draw = ImageDraw.Draw(background)
        
        title_font = self.get_font(70)
        subtitle_font = self.get_font(50)
        
        # العنوان الرئيسي
        title = "🎉 شكراً للمشاهدة! 🎉"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = 600
        
        draw.text((title_x + 3, title_y + 3), title, font=title_font, fill='#000000')
        draw.text((title_x, title_y), title, font=title_font, fill='#FFFFFF')
        
        # دعوة للعمل
        cta_lines = [
            "👍 اضغط لايك إذا أعجبك الفيديو",
            "🔔 اشترك للمزيد من الحقائق المذهلة",
            "💬 شاركنا رأيك في التعليقات"
        ]
        
        y_offset = 750
        for line in cta_lines:
            line_bbox = draw.textbbox((0, 0), line, font=subtitle_font)
            line_width = line_bbox[2] - line_bbox[0]
            line_x = (self.width - line_width) // 2
            
            draw.text((line_x + 2, y_offset + 2), line, font=subtitle_font, fill='#000000')
            draw.text((line_x, y_offset), line, font=subtitle_font, fill='#FFFFFF')
            y_offset += 80
        
        return background

    def create_video(self):
        """إنشاء الفيديو النهائي"""
        print("🎬 بدء إنشاء فيديو YouTube Short...")
        
        clips = []
        
        # 1. شريحة المقدمة
        print("📝 إنشاء شريحة المقدمة...")
        intro_slide = self.create_intro_slide()
        
        # حفظ مؤقت
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            intro_slide.save(tmp.name)
            intro_clip = ImageClip(tmp.name).set_duration(self.intro_duration)
            clips.append(intro_clip)
        
        # 2. شرائح الحقائق
        for i, fact in enumerate(self.facts, 1):
            print(f"📊 إنشاء شريحة الحقيقة {i}: {fact.get('category', 'غير محدد')}")
            fact_slide = self.create_fact_slide(fact, i)
            
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                fact_slide.save(tmp.name)
                fact_clip = ImageClip(tmp.name).set_duration(self.slide_duration)
                clips.append(fact_clip)
        
        # 3. شريحة الخاتمة
        print("🎉 إنشاء شريحة الخاتمة...")
        outro_slide = self.create_outro_slide()
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            outro_slide.save(tmp.name)
            outro_clip = ImageClip(tmp.name).set_duration(self.outro_duration)
            clips.append(outro_clip)
        
        # دمج جميع الشرائح
        print("🎞️ دمج الشرائح...")
        final_video = concatenate_videoclips(clips, method="compose")
        
        # إضافة تأثيرات انتقالية
        print("✨ إضافة التأثيرات...")
        final_video = final_video.fadein(0.5).fadeout(0.5)
        
        # حفظ الفيديو
        output_path = "simple_youtube_short_5_facts.mp4"
        print(f"💾 حفظ الفيديو: {output_path}")
        
        final_video.write_videofile(
            output_path,
            fps=self.fps,
            codec='libx264',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        print(f"✅ تم إنشاء الفيديو بنجاح: {output_path}")
        print(f"⏱️ المدة الإجمالية: {final_video.duration:.1f} ثانية")
        print(f"📱 التنسيق: {self.width}x{self.height} (9:16)")
        
        return output_path

def main():
    """الدالة الرئيسية"""
    creator = SimpleYouTubeShortCreator()
    video_path = creator.create_video()
    
    print("\n" + "="*50)
    print("🎊 تم إنشاء فيديو YouTube Short بنجاح!")
    print(f"📁 مسار الملف: {video_path}")
    print("📱 جاهز للرفع على YouTube Shorts")
    print("="*50)

if __name__ == "__main__":
    main()
