from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

def enhance_excel_file():
    """تحسين تنسيق ملف Excel الموجود"""
    
    try:
        # فتح الملف الموجود
        wb = load_workbook('أفضل_الدول_العربية_من_حيث_الطبيعة.xlsx')
        ws = wb.active
        
        # تحسين الخطوط العربية
        arabic_font = Font(name='<PERSON><PERSON>', size=12)
        header_font = Font(name='Amiri', size=14, bold=True, color='FFFFFF')
        
        # تطبيق الخط العربي على جميع الخلايا
        for row in ws.iter_rows():
            for cell in row:
                if cell.row == 1:
                    cell.font = header_font
                else:
                    cell.font = arabic_font
        
        # تحسين المحاذاة للنص العربي
        rtl_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True, text_rotation=0)
        
        for row in ws.iter_rows():
            for cell in row:
                cell.alignment = rtl_alignment
        
        # حفظ الملف المحسن
        wb.save('أفضل_الدول_العربية_من_حيث_الطبيعة_محسن.xlsx')
        print("تم تحسين الملف وحفظه باسم: أفضل_الدول_العربية_من_حيث_الطبيعة_محسن.xlsx")
        
    except Exception as e:
        print(f"خطأ في تحسين الملف: {e}")

if __name__ == "__main__":
    enhance_excel_file()
