#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def create_arabic_nature_excel():
    """إنشاء ملف Excel لأفضل الدول العربية من حيث الطبيعة"""
    
    # البيانات
    data = {
        'الدولة': [
            'المغرب',
            'لبنان', 
            'الأردن',
            'عُمان',
            'اليمن'
        ],
        'أشهر المعالم الطبيعية': [
            'جبال الأطلس، صحراء مرزوقة، شلالات أوزود، ساحل أكادير',
            'أرز الرب، مغارة جعيتا، وادي قاديشا، ساحل صور',
            'البحر الميت، وادي رم، محمية ضانا، العقبة',
            'جبال الحجر، صحراء الربع الخالي، وادي شاب، ساحل صلالة',
            'جزيرة سقطرى، جبال الحراز، وادي حضرموت، ساحل المهرة'
        ],
        'نوع الطبيعة': [
            'جبال، صحارى، شواطئ، واحات',
            'جبال، مغارات، أودية، شواطئ',
            'بحيرات مالحة، صحارى، محميات، شواطئ',
            'جبال، صحارى، أودية، شواطئ استوائية',
            'جزر نادرة، جبال، أودية، شواطئ'
        ],
        'ما يميزها': [
            'تنوع مناخي فريد من الثلوج إلى الصحراء',
            'طبيعة جبلية خلابة مع مناخ متوسطي',
            'مواقع أثرية طبيعية وتنوع جيولوجي',
            'طبيعة استوائية نادرة في الخليج',
            'تنوع حيوي فريد وأشجار نادرة'
        ],
        'أفضل وقت للزيارة': [
            'مارس - مايو، سبتمبر - نوفمبر',
            'أبريل - يونيو، سبتمبر - نوفمبر', 
            'مارس - مايو، سبتمبر - نوفمبر',
            'أكتوبر - أبريل',
            'أكتوبر - مارس'
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # إنشاء workbook جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "أفضل الدول العربية طبيعياً"
    
    # إضافة البيانات
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # تنسيق العناوين
    header_font = Font(name='Arabic Typesetting', size=14, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='2E8B57', end_color='2E8B57', fill_type='solid')  # أخضر داكن
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # تطبيق تنسيق العناوين
    for col in range(1, len(df.columns) + 1):
        cell = ws.cell(row=1, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # تنسيق البيانات
    data_font = Font(name='Arabic Typesetting', size=12)
    data_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # ألوان الصفوف المتناوبة
    light_fill = PatternFill(start_color='F0F8FF', end_color='F0F8FF', fill_type='solid')  # أزرق فاتح
    white_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')  # أبيض
    
    # تطبيق تنسيق البيانات والألوان المتناوبة
    for row in range(2, len(df) + 2):
        fill = light_fill if row % 2 == 0 else white_fill
        for col in range(1, len(df.columns) + 1):
            cell = ws.cell(row=row, column=col)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.fill = fill
    
    # إضافة حدود للجدول
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in range(1, len(df) + 2):
        for col in range(1, len(df.columns) + 1):
            ws.cell(row=row, column=col).border = thin_border
    
    # تعديل عرض الأعمدة لتناسب المحتوى
    column_widths = {
        'A': 15,  # الدولة
        'B': 45,  # أشهر المعالم الطبيعية
        'C': 25,  # نوع الطبيعة
        'D': 35,  # ما يميزها
        'E': 25   # أفضل وقت للزيارة
    }
    
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # تعديل ارتفاع الصفوف
    for row in range(1, len(df) + 2):
        ws.row_dimensions[row].height = 60 if row == 1 else 80
    
    # حفظ الملف
    filename = 'أفضل_الدول_العربية_من_حيث_الطبيعة.xlsx'
    wb.save(filename)
    
    print(f"تم إنشاء الملف بنجاح: {filename}")
    print("الملف يحتوي على:")
    print("- ورقة واحدة بعنوان: أفضل الدول العربية طبيعياً")
    print("- جدول منسق بـ 5 دول عربية")
    print("- عناوين عريضة ومحاذاة مركزية")
    print("- ألوان متناوبة للصفوف")
    print("- جميع المحتويات مرئية بدون تمرير")
    
    return filename

if __name__ == "__main__":
    create_arabic_nature_excel()
