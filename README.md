# 🎬 منشئ فيديو YouTube Short - 5 حقائق مدهشة

## 📝 الوصف
هذا المشروع ينشئ فيديو YouTube Short مدته 60 ثانية يعرض 5 حقائق مدهشة من مجالات متنوعة (العلوم، الجغرافيا، التاريخ، الفضاء، جسم الإنسان).

## ✨ المميزات
- 📱 تنسيق عمودي 9:16 مناسب لـ YouTube Shorts
- 🎨 تصميم جذاب مع خلفيات متدرجة وتأثيرات بصرية
- 🔤 نصوص عربية واضحة مع خطوط عالية الجودة
- 🎵 موسيقى خلفية ومؤثرات صوتية
- ⏱️ مدة مثالية 60 ثانية
- 🎭 رموز تعبيرية وعناصر تفاعلية
- 🌈 ألوان متنوعة لكل حقيقة

## 📋 المتطلبات
- Python 3.7 أو أحدث
- المكتبات المطلوبة (ستُثبت تلقائياً):
  - Pillow (معالجة الصور)
  - MoviePy (إنشاء الفيديو)
  - NumPy (العمليات الرياضية)

## 🚀 طريقة الاستخدام

### الطريقة السهلة (مستحسنة):
```bash
python run_youtube_short.py
```

### الطريقة اليدوية:
1. تثبيت المكتبات:
```bash
pip install -r requirements.txt
```

2. تشغيل السكربت:
```bash
python enhanced_youtube_short.py
```

## 📁 الملفات المتضمنة

- `enhanced_youtube_short.py` - السكربت الرئيسي المحسن
- `youtube_short_facts.py` - النسخة الأساسية
- `run_youtube_short.py` - سكربت التشغيل السهل
- `requirements.txt` - قائمة المكتبات المطلوبة
- `README.md` - هذا الملف

## 🎯 محتوى الفيديو

### المقدمة (5 ثوانٍ):
- ترحيب وتشويق
- عنوان جذاب مع رموز تعبيرية

### الحقائق (50 ثانية - 10 ثوانٍ لكل حقيقة):
1. **🌌 حقيقة فضائية**: حجم الشمس مقارنة بالأرض
2. **🧠 حقيقة عن الدماغ**: استهلاك الطاقة
3. **🌍 حقيقة جغرافية**: حجم المحيط الهادئ
4. **🏛️ حقيقة تاريخية**: الأهرامات والماموث
5. **🦋 حقيقة طبيعية**: تذوق الفراشات

### الخاتمة (5 ثوانٍ):
- شكر للمشاهدة
- دعوة للإعجاب والاشتراك

## 🎨 التصميم

### الألوان:
- خلفيات متدرجة متنوعة لكل حقيقة
- نصوص بيضاء مع تأثيرات الظل
- ألوان تمييز مختلفة لكل قسم

### التأثيرات:
- انتقالات سلسة (fade in/out)
- تأثيرات ثلاثية الأبعاد للنصوص
- عناصر زخرفية متحركة
- إطارات وخلفيات شبه شفافة

## 📱 المواصفات التقنية

- **الدقة**: 1080x1920 (9:16)
- **معدل الإطارات**: 30 FPS
- **التنسيق**: MP4
- **الترميز**: H.264
- **الصوت**: AAC (إذا توفر)
- **المدة**: 60 ثانية

## 🔧 التخصيص

يمكنك تخصيص الفيديو عبر تعديل:
- الحقائق في قائمة `self.facts`
- الألوان في `background_color` و `accent_color`
- مدة كل شريحة في `slide_duration`
- الخطوط والأحجام في دوال `get_font()`

## 📤 الاستخدام

الفيديو الناتج جاهز للرفع مباشرة على:
- YouTube Shorts
- Instagram Reels
- TikTok
- Facebook Stories

## 🐛 حل المشاكل

### مشكلة في تثبيت MoviePy:
```bash
pip install imageio-ffmpeg
```

### مشكلة في الخطوط العربية:
- تأكد من وجود خطوط عربية في النظام
- أو ضع ملف خط عربي في مجلد المشروع

### مشكلة في الصوت:
- تأكد من تثبيت FFmpeg
- أو استخدم النسخة الأساسية بدون صوت

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Python 3.7+
2. تأكد من تثبيت جميع المكتبات
3. تأكد من وجود مساحة كافية على القرص
4. تأكد من صلاحيات الكتابة في المجلد

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

---

🎉 **استمتع بإنشاء فيديوهات YouTube Shorts رائعة!** 🎉
