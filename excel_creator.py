from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

# إنشاء workbook جديد
wb = Workbook()
ws = wb.active
ws.title = "أفضل الدول العربية طبيعياً"

# إضافة العناوين
headers = ['الدولة', 'أشهر المعالم الطبيعية', 'نوع الطبيعة', 'ما يميزها', 'أفضل وقت للزيارة']
ws.append(headers)

# إضافة البيانات
data = [
    ['المغرب', 'جبال الأطلس، صحراء مرزوقة، شلالات أوزود، ساحل أكادير', 'جبال، صحارى، شواطئ، واحات', 'تنوع مناخي فريد من الثلوج إلى الصحراء', 'مارس - مايو، سبتمبر - نوفمبر'],
    ['لبنان', 'أرز الرب، مغارة جعيتا، وادي قاديشا، ساحل صور', 'جبال، مغارات، أودية، شواطئ', 'طبيعة جبلية خلابة مع مناخ متوسطي', 'أبريل - يونيو، سبتمبر - نوفمبر'],
    ['الأردن', 'البحر الميت، وادي رم، محمية ضانا، العقبة', 'بحيرات مالحة، صحارى، محميات، شواطئ', 'مواقع أثرية طبيعية وتنوع جيولوجي', 'مارس - مايو، سبتمبر - نوفمبر'],
    ['عُمان', 'جبال الحجر، صحراء الربع الخالي، وادي شاب، ساحل صلالة', 'جبال، صحارى، أودية، شواطئ استوائية', 'طبيعة استوائية نادرة في الخليج', 'أكتوبر - أبريل'],
    ['اليمن', 'جزيرة سقطرى، جبال الحراز، وادي حضرموت، ساحل المهرة', 'جزر نادرة، جبال، أودية، شواطئ', 'تنوع حيوي فريد وأشجار نادرة', 'أكتوبر - مارس']
]

for row in data:
    ws.append(row)

# تنسيق العناوين
header_font = Font(name='Arabic Typesetting', size=14, bold=True, color='FFFFFF')
header_fill = PatternFill(start_color='2E8B57', end_color='2E8B57', fill_type='solid')
header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

for col in range(1, 6):
    cell = ws.cell(row=1, column=col)
    cell.font = header_font
    cell.fill = header_fill
    cell.alignment = header_alignment

# تنسيق البيانات
data_font = Font(name='Arabic Typesetting', size=12)
data_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

# ألوان الصفوف المتناوبة
light_fill = PatternFill(start_color='F0F8FF', end_color='F0F8FF', fill_type='solid')
white_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

for row in range(2, 7):
    fill = light_fill if row % 2 == 0 else white_fill
    for col in range(1, 6):
        cell = ws.cell(row=row, column=col)
        cell.font = data_font
        cell.alignment = data_alignment
        cell.fill = fill

# إضافة حدود
thin_border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

for row in range(1, 7):
    for col in range(1, 6):
        ws.cell(row=row, column=col).border = thin_border

# تعديل عرض الأعمدة
ws.column_dimensions['A'].width = 15
ws.column_dimensions['B'].width = 45
ws.column_dimensions['C'].width = 25
ws.column_dimensions['D'].width = 35
ws.column_dimensions['E'].width = 25

# تعديل ارتفاع الصفوف
ws.row_dimensions[1].height = 60
for row in range(2, 7):
    ws.row_dimensions[row].height = 80

# حفظ الملف
filename = 'أفضل_الدول_العربية_من_حيث_الطبيعة.xlsx'
wb.save(filename)

print(f"تم إنشاء الملف بنجاح: {filename}")
print("الملف يحتوي على:")
print("- ورقة واحدة بعنوان: أفضل الدول العربية طبيعياً")
print("- جدول منسق بـ 5 دول عربية")
print("- عناوين عريضة ومحاذاة مركزية")
print("- ألوان متناوبة للصفوف")
print("- جميع المحتويات مرئية بدون تمرير")
