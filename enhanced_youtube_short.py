#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube Short محسن - 5 حقائق مدهشة مع موسيقى ومؤثرات
"""

import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np
from moviepy.editor import *
import textwrap
import random
import math

class EnhancedYouTubeShortCreator:
    def __init__(self):
        self.width = 1080
        self.height = 1920
        self.fps = 30
        self.slide_duration = 10
        self.intro_duration = 5
        self.outro_duration = 5

        # الحقائق المدهشة مع تحسينات
        self.facts = [
            {
                'title': '🌌 حقيقة فضائية مذهلة',
                'text': 'يمكن لمليون كرة أرضية\nأن تتسع داخل الشمس!\nالشمس أكبر من الأرض\nبـ 1.3 مليون مرة 🤯',
                'emoji': '🌞',
                'category': 'الفضاء',
                'background_color': ['#667eea', '#764ba2'],
                'accent_color': '#FFD700'
            },
            {
                'title': '🧠 حقيقة عن الدماغ',
                'text': 'دماغك يستهلك 20%\nمن طاقة جسمك رغم أنه\nيشكل 2% فقط من وزنك!\nإنه كمبيوتر خارق 💻',
                'emoji': '🧠',
                'category': 'جسم الإنسان',
                'background_color': ['#f093fb', '#f5576c'],
                'accent_color': '#00FFFF'
            },
            {
                'title': '🌍 حقيقة جغرافية',
                'text': 'المحيط الهادئ أكبر\nمن كل اليابسة مجتمعة!\nيغطي ثلث سطح الأرض\nويحتوي على 50% من المياه 🌊',
                'emoji': '🌊',
                'category': 'الجغرافيا',
                'background_color': ['#4facfe', '#00f2fe'],
                'accent_color': '#FF6B35'
            },
            {
                'title': '🏛️ حقيقة تاريخية',
                'text': 'الأهرامات بُنيت قبل\nوجود الماموث!\nآخر ماموث عاش\nقبل 4000 سنة فقط 🐘',
                'emoji': '🏺',
                'category': 'التاريخ',
                'background_color': ['#fa709a', '#fee140'],
                'accent_color': '#8A2BE2'
            },
            {
                'title': '🦋 حقيقة طبيعية',
                'text': 'الفراشات تتذوق بأقدامها!\nولديها مستقبلات تذوق\nفي أرجلها للعثور\nعلى الطعام المناسب 👅',
                'emoji': '🦋',
                'category': 'الطبيعة',
                'background_color': ['#a8edea', '#fed6e3'],
                'accent_color': '#FF1493'
            }
        ]

    def create_animated_gradient(self, colors, width, height, frame_number=0):
        """إنشاء خلفية متدرجة متحركة"""
        image = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(image)

        # إضافة حركة للتدرج
        offset = math.sin(frame_number * 0.1) * 0.2

        for y in range(height):
            ratio = (y / height) + offset
            ratio = max(0, min(1, ratio))

            color1 = tuple(int(colors[0][i:i+2], 16) for i in (1, 3, 5))
            color2 = tuple(int(colors[1][i:i+2], 16) for i in (1, 3, 5))

            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)

            draw.line([(0, y), (width, y)], fill=(r, g, b))

        return image

    def add_decorative_elements(self, draw, width, height, accent_color):
        """إضافة عناصر زخرفية"""
        # رسم دوائر زخرفية
        for i in range(5):
            x = random.randint(50, width - 50)
            y = random.randint(100, height - 100)
            radius = random.randint(20, 60)
            alpha = random.randint(30, 80)

            # محاكاة الشفافية بخلط الألوان
            circle_color = accent_color
            draw.ellipse([x-radius, y-radius, x+radius, y+radius],
                        outline=circle_color, width=3)

    def get_font(self, size):
        """الحصول على خط عربي مناسب"""
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            "arial.ttf",
            None
        ]

        for font_path in font_paths:
            try:
                if font_path:
                    return ImageFont.truetype(font_path, size)
                else:
                    return ImageFont.load_default()
            except:
                continue
        return ImageFont.load_default()

    def create_intro_slide(self):
        """إنشاء شريحة المقدمة المحسنة"""
        background = self.create_animated_gradient(['#667eea', '#764ba2'], self.width, self.height)
        draw = ImageDraw.Draw(background)

        # إضافة عناصر زخرفية
        self.add_decorative_elements(draw, self.width, self.height, '#FFD700')

        # الخطوط
        title_font = self.get_font(85)
        subtitle_font = self.get_font(55)

        # العنوان الرئيسي مع تأثير ثلاثي الأبعاد
        title = "🔥 5 حقائق مذهلة 🔥"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = 600

        # تأثير ثلاثي الأبعاد
        for offset in range(5, 0, -1):
            gray_value = 50 + offset * 20
            shadow_color = f"#{gray_value:02x}{gray_value:02x}{gray_value:02x}"
            draw.text((title_x + offset, title_y + offset), title,
                     font=title_font, fill=shadow_color)

        draw.text((title_x, title_y), title, font=title_font, fill='#FFFFFF')

        # العنوان الفرعي
        subtitle = "ستصدمك هذه المعلومات! 🤯✨"
        subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (self.width - subtitle_width) // 2
        subtitle_y = 750

        draw.text((subtitle_x + 3, subtitle_y + 3), subtitle, font=subtitle_font, fill='#000000')
        draw.text((subtitle_x, subtitle_y), subtitle, font=subtitle_font, fill='#FFD23F')

        # إضافة شريط زخرفي
        draw.rectangle([100, 850, self.width-100, 870], fill='#FFD23F')
        draw.rectangle([150, 880, self.width-150, 900], fill='#FF6B35')

        return background

    def create_fact_slide(self, fact, fact_number):
        """إنشاء شريحة حقيقة محسنة"""
        background = self.create_animated_gradient(fact['background_color'], self.width, self.height)
        draw = ImageDraw.Draw(background)

        # إضافة عناصر زخرفية
        self.add_decorative_elements(draw, self.width, self.height, fact['accent_color'])

        # الخطوط
        number_font = self.get_font(130)
        title_font = self.get_font(65)
        text_font = self.get_font(48)
        emoji_font = self.get_font(160)

        # رقم الحقيقة مع إطار
        number_text = f"#{fact_number}"
        number_bbox = draw.textbbox((0, 0), number_text, font=number_font)
        number_width = number_bbox[2] - number_bbox[0]
        number_height = number_bbox[3] - number_bbox[1]
        number_x = (self.width - number_width) // 2
        number_y = 150

        # رسم إطار للرقم
        padding = 20
        draw.rounded_rectangle([number_x - padding, number_y - padding,
                               number_x + number_width + padding,
                               number_y + number_height + padding],
                              radius=15, fill=fact['accent_color'], outline='#FFFFFF', width=3)

        draw.text((number_x, number_y), number_text, font=number_font, fill='#FFFFFF')

        # الرمز التعبيري مع تأثير توهج
        emoji = fact['emoji']
        emoji_bbox = draw.textbbox((0, 0), emoji, font=emoji_font)
        emoji_width = emoji_bbox[2] - emoji_bbox[0]
        emoji_x = (self.width - emoji_width) // 2
        emoji_y = 320

        # تأثير توهج للرمز التعبيري
        for offset in range(3, 0, -1):
            draw.text((emoji_x + offset, emoji_y + offset), emoji,
                     font=emoji_font, fill='#000000')
        draw.text((emoji_x, emoji_y), emoji, font=emoji_font)

        # العنوان مع خلفية شفافة
        title = fact['title']
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_height = title_bbox[3] - title_bbox[1]
        title_x = (self.width - title_width) // 2
        title_y = 520

        # خلفية شبه شفافة للعنوان
        draw.rounded_rectangle([title_x - 20, title_y - 10,
                               title_x + title_width + 20,
                               title_y + title_height + 10],
                              radius=10, fill='#000000')

        draw.text((title_x, title_y), title, font=title_font, fill='#FFFFFF')

        # النص الرئيسي مع تنسيق محسن
        text_lines = fact['text'].split('\n')
        y_offset = 650

        for line in text_lines:
            line_bbox = draw.textbbox((0, 0), line, font=text_font)
            line_width = line_bbox[2] - line_bbox[0]
            line_height = line_bbox[3] - line_bbox[1]
            line_x = (self.width - line_width) // 2

            # خلفية للنص
            draw.rounded_rectangle([line_x - 15, y_offset - 5,
                                   line_x + line_width + 15,
                                   y_offset + line_height + 5],
                                  radius=8, fill='#000000')

            draw.text((line_x, y_offset), line, font=text_font, fill='#FFFFFF')
            y_offset += 65

        return background

    def create_outro_slide(self):
        """إنشاء شريحة الخاتمة المحسنة"""
        background = self.create_animated_gradient(['#fa709a', '#fee140'], self.width, self.height)
        draw = ImageDraw.Draw(background)

        # إضافة عناصر زخرفية
        self.add_decorative_elements(draw, self.width, self.height, '#FFFFFF')

        # الخطوط
        title_font = self.get_font(75)
        subtitle_font = self.get_font(52)

        # العنوان الرئيسي
        title = "🎉 شكراً للمشاهدة! 🎉"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = 500

        # تأثير ثلاثي الأبعاد للعنوان
        for offset in range(4, 0, -1):
            gray_value = 30 + offset * 15
            shadow_color = f"#{gray_value:02x}{gray_value:02x}{gray_value:02x}"
            draw.text((title_x + offset, title_y + offset), title,
                     font=title_font, fill=shadow_color)
        draw.text((title_x, title_y), title, font=title_font, fill='#FFFFFF')

        # دعوات العمل مع أيقونات
        cta_lines = [
            "👍 اضغط لايك إذا أعجبك",
            "🔔 اشترك للمزيد من الحقائق",
            "💬 شاركنا رأيك بالتعليقات",
            "🔄 شارك مع أصدقائك"
        ]

        y_offset = 650
        for line in cta_lines:
            line_bbox = draw.textbbox((0, 0), line, font=subtitle_font)
            line_width = line_bbox[2] - line_bbox[0]
            line_height = line_bbox[3] - line_bbox[1]
            line_x = (self.width - line_width) // 2

            # خلفية للنص
            draw.rounded_rectangle([line_x - 20, y_offset - 8,
                                   line_x + line_width + 20,
                                   y_offset + line_height + 8],
                                  radius=12, fill='#000000')

            draw.text((line_x, y_offset), line, font=subtitle_font, fill='#FFFFFF')
            y_offset += 75

        return background

    def create_video_with_audio(self):
        """إنشاء الفيديو مع الصوت"""
        print("🎬 بدء إنشاء فيديو YouTube Short محسن...")

        # إنشاء مجلد للصور المؤقتة
        temp_dir = "temp_slides_enhanced"
        os.makedirs(temp_dir, exist_ok=True)

        clips = []

        # 1. شريحة المقدمة
        print("📝 إنشاء شريحة المقدمة...")
        intro_slide = self.create_intro_slide()
        intro_path = os.path.join(temp_dir, "intro.png")
        intro_slide.save(intro_path)
        intro_clip = ImageClip(intro_path).set_duration(self.intro_duration)
        clips.append(intro_clip)

        # 2. شرائح الحقائق
        for i, fact in enumerate(self.facts, 1):
            print(f"📊 إنشاء شريحة الحقيقة {i}: {fact['category']}")
            fact_slide = self.create_fact_slide(fact, i)
            fact_path = os.path.join(temp_dir, f"fact_{i}.png")
            fact_slide.save(fact_path)
            fact_clip = ImageClip(fact_path).set_duration(self.slide_duration)
            clips.append(fact_clip)

        # 3. شريحة الخاتمة
        print("🎉 إنشاء شريحة الخاتمة...")
        outro_slide = self.create_outro_slide()
        outro_path = os.path.join(temp_dir, "outro.png")
        outro_slide.save(outro_path)
        outro_clip = ImageClip(outro_path).set_duration(self.outro_duration)
        clips.append(outro_clip)

        # دمج جميع الشرائح
        print("🎞️ دمج الشرائح...")
        final_video = concatenate_videoclips(clips, method="compose")

        # إضافة تأثيرات انتقالية متقدمة
        print("✨ إضافة التأثيرات المتقدمة...")
        final_video = final_video.fadein(1.0).fadeout(1.0)

        # إنشاء صوت تركيبي بسيط (نغمات)
        print("🎵 إضافة الصوت...")
        try:
            # إنشاء نغمة خلفية بسيطة
            duration = final_video.duration
            sample_rate = 44100

            # إنشاء نغمة موسيقية بسيطة
            def make_frame_audio(t):
                # نغمة هادئة ومشجعة
                freq1 = 440  # نوتة A
                freq2 = 554  # نوتة C#
                freq3 = 659  # نوتة E

                wave1 = 0.3 * np.sin(2 * np.pi * freq1 * t)
                wave2 = 0.2 * np.sin(2 * np.pi * freq2 * t)
                wave3 = 0.1 * np.sin(2 * np.pi * freq3 * t)

                return wave1 + wave2 + wave3

            audio_clip = AudioClip(make_frame_audio, duration=duration)
            final_video = final_video.set_audio(audio_clip)

        except Exception as e:
            print(f"⚠️ تعذر إضافة الصوت: {e}")
            print("📹 سيتم إنشاء الفيديو بدون صوت")

        # حفظ الفيديو
        output_path = "enhanced_youtube_short_5_facts.mp4"
        print(f"💾 حفظ الفيديو: {output_path}")

        final_video.write_videofile(
            output_path,
            fps=self.fps,
            codec='libx264',
            audio_codec='aac' if final_video.audio else None,
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )

        # تنظيف الملفات المؤقتة
        import shutil
        shutil.rmtree(temp_dir)

        print(f"✅ تم إنشاء الفيديو المحسن بنجاح: {output_path}")
        print(f"⏱️ المدة الإجمالية: {final_video.duration:.1f} ثانية")
        print(f"📱 التنسيق: {self.width}x{self.height} (9:16)")
        print(f"🎵 الصوت: {'متضمن' if final_video.audio else 'غير متضمن'}")

        return output_path

def main():
    """الدالة الرئيسية"""
    creator = EnhancedYouTubeShortCreator()
    video_path = creator.create_video_with_audio()

    print("\n" + "="*60)
    print("🎊 تم إنشاء فيديو YouTube Short المحسن بنجاح!")
    print(f"📁 مسار الملف: {video_path}")
    print("📱 جاهز للرفع على YouTube Shorts")
    print("🌟 يتضمن: تأثيرات بصرية، انتقالات سلسة، وصوت")
    print("="*60)

if __name__ == "__main__":
    main()
