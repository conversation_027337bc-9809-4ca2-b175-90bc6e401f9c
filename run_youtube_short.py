#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت تشغيل سهل لإنشاء فيديو YouTube Short
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المكتبات المطلوبة...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False

def check_libraries():
    """فحص المكتبات"""
    required_libs = ['PIL', 'moviepy', 'numpy']
    missing_libs = []

    for lib in required_libs:
        try:
            if lib == 'PIL':
                import PIL
            elif lib == 'moviepy':
                import moviepy
            elif lib == 'numpy':
                import numpy
            print(f"✅ {lib} متوفرة")
        except ImportError:
            missing_libs.append(lib)
            print(f"❌ {lib} غير متوفرة")

    return len(missing_libs) == 0

def main():
    """الدالة الرئيسية"""
    print("🎬 مرحباً بك في منشئ فيديو YouTube Short")
    print("=" * 50)

    # فحص المكتبات
    if not check_libraries():
        print("\n📦 بعض المكتبات مفقودة، سيتم تثبيتها...")
        if not install_requirements():
            print("❌ فشل في تثبيت المكتبات. يرجى تثبيتها يدوياً:")
            print("pip install Pillow moviepy numpy")
            return

    print("\n🚀 بدء إنشاء الفيديو...")

    # تشغيل السكربت المبسط (أكثر استقراراً)
    try:
        from simple_youtube_short import SimpleYouTubeShortCreator

        creator = SimpleYouTubeShortCreator()
        video_path = creator.create_video()

        print(f"\n🎉 تم إنشاء الفيديو بنجاح!")
        print(f"📁 مسار الملف: {video_path}")

        # فتح مجلد الملف
        if os.path.exists(video_path):
            print("📂 فتح مجلد الملف...")
            if sys.platform == "win32":
                os.startfile(os.path.dirname(os.path.abspath(video_path)))
            elif sys.platform == "darwin":
                subprocess.call(["open", os.path.dirname(os.path.abspath(video_path))])
            else:
                subprocess.call(["xdg-open", os.path.dirname(os.path.abspath(video_path))])

    except Exception as e:
        print(f"❌ حدث خطأ: {e}")
        print("🔧 يرجى التأكد من تثبيت جميع المكتبات المطلوبة")

if __name__ == "__main__":
    main()
