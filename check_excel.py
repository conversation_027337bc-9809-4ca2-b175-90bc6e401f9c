import openpyxl

try:
    wb = openpyxl.load_workbook('أفضل_الدول_العربية_من_حيث_الطبيعة.xlsx')
    ws = wb.active
    print(f"Sheet name: {ws.title}")
    print(f"Data rows: {ws.max_row}")
    print(f"Data columns: {ws.max_column}")
    print("File exists and is readable!")
    
    # طباعة العناوين
    print("\nHeaders:")
    for col in range(1, ws.max_column + 1):
        print(f"Column {col}: {ws.cell(row=1, column=col).value}")
        
except Exception as e:
    print(f"Error: {e}")
